import {DriverFactory} from './Driver/DriverFactory.ts';
import UrlSetFactory from './UrlSetFactory.ts';
import FileWriter from './FileWriter.ts';
import TestCase from './TestCase.ts';
import {DriverInterface} from './Driver/DriverInterface.ts';
import OracleConfig from '@/shared/types/Common/OracleConfig.ts';
import brands from '@/shared/config/overrides/brands.ts';
import TestCaseFactory from './TestCaseFactory.ts';
import ClusterManager from './ClusterManager.ts';
import Brand from '@/backend/Common/Brand.ts';
import {uniq} from 'lodash-es';
import Application from '@/backend/Lib/Application.ts';
import type UrlSet from '@/shared/types/Test/UrlSet.ts';
import type Browser from '@/shared/types/Config/Browser.ts';
import type Viewport from '@/shared/types/Config/Viewport.ts';

interface TestItemConfig {
    urls: UrlSet[];
    browsers: Browser[];
    viewports: Viewport[];
    drivers: string[];
    brand: Brand;
}

interface TestItems {
    [brandSlug: string]: TestItemConfig[];
}

class TestCaseCreator {
    private readonly testCases: TestCase[] = [];
    private readonly oracleConfig: OracleConfig;
    private readonly clusterManager: ClusterManager;

    constructor(config: OracleConfig, clusterManager: ClusterManager) {
        if (!config) {
            throw new Error('OracleConfig is required');
        }
        if (!clusterManager) {
            throw new Error('ClusterManager is required');
        }

        this.oracleConfig = config;
        this.clusterManager = clusterManager;
    }

    public createTestItems(date: string): TestItems {
        if (!date || typeof date !== 'string') {
            throw new Error('Valid date string is required');
        }

        const screenshotPath = Application.screenshotPath;
        const testItems: TestItems = {};

        try {
            for (const brandConfig of this.oracleConfig.brands) {
                const domains = this.getDomains(brandConfig);
                testItems[brandConfig.slug] = domains.map(domain => ({
                    urls: this.oracleConfig.urls.map(url => UrlSetFactory.create(domain, url)),
                    browsers: this.oracleConfig.browsers,
                    viewports: this.oracleConfig.viewports,
                    drivers: this.oracleConfig.drivers,
                    brand: brandConfig,
                }));
            }

            this.writeTestItemsToFile(screenshotPath, date, testItems);
            return testItems;
        } catch (error) {
            throw new Error(`Failed to create test items: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }

    private writeTestItemsToFile(screenshotPath: string, date: string, testItems: TestItems): void {
        try {
            FileWriter.writeSafe(
                `${screenshotPath}/${date}/test.json`,
                JSON.stringify(testItems, undefined, 2),
                false,
            );
        } catch (error) {
            throw new Error(`Failed to write test items to file: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }

    public getTestCases(): readonly TestCase[] {
        return [...this.testCases];
    }

    public async createTestCases(date: string): Promise<void> {
        if (!date || typeof date !== 'string') {
            throw new Error('Valid date string is required');
        }

        try {
            const drivers = this.createDrivers();
            const testItems = this.createTestItems(date);

            for (const [brandSlug, brandConfigs] of Object.entries(testItems)) {
                await this.processBrandConfigs(brandSlug, brandConfigs, drivers, date);
            }
        } catch (error) {
            throw new Error(`Failed to create test cases: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }

    private async processBrandConfigs(
        brandSlug: string,
        brandConfigs: TestItemConfig[],
        drivers: DriverInterface[],
        date: string
    ): Promise<void> {
        for (const brandConfig of brandConfigs) {
            const combinations = this.generateTestCombinations(drivers, brandConfig);

            for (const combination of combinations) {
                await this.createAndAddTestCase(combination, brandSlug, date);
            }
        }
    }

    private async createAndAddTestCase(
        combination: [DriverInterface, Browser, Viewport, UrlSet],
        brandSlug: string,
        date: string
    ): Promise<void> {
        const [driver, browser, viewport, url] = combination;

        try {
            const cluster = await this.clusterManager.initClusterForDriver(driver, browser);
            const testCase = TestCaseFactory.create(
                date, brandSlug, url, browser, viewport, driver, this.oracleConfig.user,
            ).runOnCluster(cluster);

            this.testCases.push(testCase);
        } catch (error) {
            throw new Error(`Failed to create test case for ${brandSlug}: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }

    private getDomains(brandConfig: Brand): string[] {
        try {
            const brandConfigOverrides = brands[brandConfig.slug] || null;
            let additionalDomains: string[] = [];

            if (brandConfigOverrides && Array.isArray(brandConfigOverrides.additionalDomains)) {
                additionalDomains = brandConfigOverrides.additionalDomains
                    .map((subDomain: string) => brandConfig.getSubDomainByPrefix(subDomain))
                    .filter((domain: string | null) => domain !== null) as string[];
            }

            const primaryDomain = brandConfig.getSubDomainByPrefix(brandConfig.getSubDomain());
            const allDomains = [primaryDomain, ...additionalDomains].filter(Boolean);

            return uniq(allDomains);
        } catch (error) {
            throw new Error(`Failed to get domains for brand ${brandConfig.slug}: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }

    private createDrivers(): DriverInterface[] {
        try {
            return this.oracleConfig.drivers.map(driverName => DriverFactory.createDriver(driverName));
        } catch (error) {
            throw new Error(`Failed to create drivers: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }

    private generateTestCombinations(
        drivers: DriverInterface[],
        brandConfig: TestItemConfig
    ): Array<[DriverInterface, Browser, Viewport, UrlSet]> {
        const combinations: Array<[DriverInterface, Browser, Viewport, UrlSet]> = [];

        for (const driver of drivers) {
            for (const browser of brandConfig.browsers) {
                for (const viewport of brandConfig.viewports) {
                    for (const url of brandConfig.urls) {
                        combinations.push([driver, browser, viewport, url]);
                    }
                }
            }
        }

        return combinations;
    }
}

export default TestCaseCreator;
