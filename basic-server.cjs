const http = require('http');
const fs = require('fs');
const path = require('path');
const url = require('url');

const PORT = process.env.PORT || 3000;

console.log('🚀 Starting Oracle basic server...');

// MIME types
const mimeTypes = {
  '.html': 'text/html',
  '.js': 'text/javascript',
  '.css': 'text/css',
  '.json': 'application/json',
  '.png': 'image/png',
  '.jpg': 'image/jpg',
  '.gif': 'image/gif',
  '.svg': 'image/svg+xml',
  '.wav': 'audio/wav',
  '.mp4': 'video/mp4',
  '.woff': 'application/font-woff',
  '.ttf': 'application/font-ttf',
  '.eot': 'application/vnd.ms-fontobject',
  '.otf': 'application/font-otf',
  '.wasm': 'application/wasm'
};

// Helper function to get content type
function getContentType(filePath) {
  const ext = path.extname(filePath).toLowerCase();
  return mimeTypes[ext] || 'application/octet-stream';
}

// Helper function to serve static files
function serveStaticFile(res, filePath) {
  fs.readFile(filePath, (err, content) => {
    if (err) {
      if (err.code === 'ENOENT') {
        res.writeHead(404, { 'Content-Type': 'text/plain' });
        res.end('File not found');
      } else {
        res.writeHead(500, { 'Content-Type': 'text/plain' });
        res.end('Server error');
      }
    } else {
      res.writeHead(200, { 
        'Content-Type': getContentType(filePath),
        'Access-Control-Allow-Origin': '*'
      });
      res.end(content);
    }
  });
}

// Helper function to handle API requests
function handleApiRequest(req, res, pathname) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');

  if (req.method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }

  if (pathname === '/api/health') {
    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({
      success: true,
      message: 'Oracle API is running',
      timestamp: new Date().toISOString(),
    }));
    return;
  }

  if (pathname === '/api/run-test' && req.method === 'POST') {
    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });
    req.on('end', () => {
      try {
        const data = JSON.parse(body);
        console.log('Test run request received:', data);
        
        // Simulate test execution
        setTimeout(() => {
          res.writeHead(200, { 'Content-Type': 'application/json' });
          res.end(JSON.stringify({
            success: true,
            data: {
              date: new Date().toISOString(),
              totalTestCases: Math.floor(Math.random() * 50) + 10,
              executionTime: Math.floor(Math.random() * 30000) + 5000,
              estimatedTime: 25000,
            },
          }));
        }, 1000);
      } catch (error) {
        res.writeHead(400, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ success: false, error: 'Invalid JSON' }));
      }
    });
    return;
  }

  if (pathname === '/api/test-status') {
    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({
      success: true,
      data: {
        status: { 
          isRunning: false,
          progress: 0,
          currentStep: '',
          startTime: null,
          estimatedTime: null,
          logs: [],
        },
        config: { 
          environments: ['local', 'production'],
          maxConcurrency: 25,
          cleanupAfterRun: true,
        },
      },
    }));
    return;
  }

  // API endpoint not found
  res.writeHead(404, { 'Content-Type': 'application/json' });
  res.end(JSON.stringify({ success: false, error: 'API endpoint not found' }));
}

// Create server
const server = http.createServer((req, res) => {
  const parsedUrl = url.parse(req.url, true);
  const pathname = parsedUrl.pathname;

  console.log(`${req.method} ${pathname} - ${req.connection.remoteAddress}`);

  // Handle API requests
  if (pathname.startsWith('/api/')) {
    handleApiRequest(req, res, pathname);
    return;
  }

  // Handle screenshots
  if (pathname.startsWith('/screenshots/')) {
    const screenshotPath = path.join(__dirname, 'public', pathname);
    serveStaticFile(res, screenshotPath);
    return;
  }

  // Handle static files
  let filePath;
  if (pathname === '/' || !pathname.includes('.')) {
    // Serve index.html for root and client-side routes
    filePath = path.join(__dirname, 'dist', 'index.html');
  } else {
    // Serve static assets
    filePath = path.join(__dirname, 'dist', pathname);
  }

  serveStaticFile(res, filePath);
});

// Graceful shutdown
const gracefulShutdown = () => {
  console.log('📴 Received shutdown signal, closing server gracefully...');
  
  server.close(() => {
    console.log('✅ HTTP server closed');
    process.exit(0);
  });
  
  // Force close after 10 seconds
  setTimeout(() => {
    console.error('❌ Could not close connections in time, forcefully shutting down');
    process.exit(1);
  }, 10000);
};

// Handle shutdown signals
process.on('SIGTERM', gracefulShutdown);
process.on('SIGINT', gracefulShutdown);

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error(`❌ Uncaught Exception: ${error.message}`);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error(`❌ Unhandled Rejection: ${reason}`);
  process.exit(1);
});

// Start server
server.listen(PORT, () => {
  console.log(`🚀 Oracle server running on port ${PORT}`);
  console.log(`📱 Frontend: http://localhost:${PORT}`);
  console.log(`🔌 API: http://localhost:${PORT}/api`);
  console.log(`📁 Screenshots: http://localhost:${PORT}/screenshots`);
  console.log(`✅ Server ready!`);
  
  if (process.env.NODE_ENV !== 'production') {
    console.log('🔧 Development mode - CORS enabled for all origins');
  }
});

module.exports = server;
