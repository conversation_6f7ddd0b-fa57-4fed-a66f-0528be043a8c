<template>
    <button
        id="theme-toggle"
        @click="toggleTheme"
        :title="`Switch to ${nextTheme} theme`"
        :aria-label="`Switch to ${nextTheme} theme`"
    >
        <span v-if="currentTheme === 'light'" class="icon dark-icon">🌙</span>
        <span v-else-if="currentTheme === 'dark'" class="icon light-icon">☀️</span>
        <span v-else class="icon auto-icon">🌓</span>
    </button>
</template>

<script setup lang="ts">
import { computed, watch, onMounted } from 'vue';
import { useAppStore } from '@/frontend/stores/modules/appStore.ts';

const appStore = useAppStore();

const currentTheme = computed(() => appStore.userPreferences.theme);

const nextTheme = computed(() => {
    switch (currentTheme.value) {
        case 'light': return 'dark';
        case 'dark': return 'auto';
        case 'auto': return 'light';
        default: return 'light';
    }
});

const toggleTheme = async (): Promise<void> => {
    const newTheme = nextTheme.value;
    await appStore.updateUserPreferences({ theme: newTheme });
    applyTheme(newTheme);
};

const applyTheme = (theme: string): void => {
    const htmlElement = document.documentElement;

    // Remove existing theme classes
    htmlElement.classList.remove('theme--light', 'theme--dark', 'theme--auto');

    // Apply new theme
    if (theme === 'auto') {
        // Use system preference
        const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
        htmlElement.classList.add(prefersDark ? 'theme--dark' : 'theme--light');
        htmlElement.setAttribute('data-theme', prefersDark ? 'dark' : 'light');
    } else {
        htmlElement.classList.add(`theme--${theme}`);
        htmlElement.setAttribute('data-theme', theme);
    }
};

// Watch for theme changes
watch(currentTheme, (newTheme) => {
    applyTheme(newTheme);
}, { immediate: true });

// Listen for system theme changes when in auto mode
onMounted(() => {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');

    const handleSystemThemeChange = () => {
        if (currentTheme.value === 'auto') {
            applyTheme('auto');
        }
    };

    mediaQuery.addEventListener('change', handleSystemThemeChange);

    // Apply initial theme
    applyTheme(currentTheme.value);
});
</script>

<style lang="scss" scoped>
#theme-toggle {
    background: none;
    height: 2.5rem;
    width: 2.5rem;
    border: none;
    color: var(--color-text-primary);
    cursor: pointer;
    padding: 0;
    border-radius: var(--border-radius-full);
    transition: all var(--transition-fast);
    position: relative;
    display: grid;
    place-items: center;
    overflow: hidden;

    &:hover {
        background-color: var(--color-background-tertiary);
        transform: rotate(15deg);
    }

    &:focus {
        outline: none;
        box-shadow: 0 0 0 2px var(--color-primary-light);
    }

    &:active {
        transform: scale(0.95);
    }

    .icon {
        font-size: var(--font-size-3xl);
        height: 100%;
        width: 100%;
        transition: all var(--transition-normal);

        &.light-icon {
            color: var(--color-warning);
        }

        &.dark-icon {
            color: var(--color-text-secondary);
        }
    }
}
</style>